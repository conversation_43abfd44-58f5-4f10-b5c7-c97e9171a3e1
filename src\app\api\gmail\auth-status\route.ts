import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthenticatedRequest } from '@/lib/jwt-middleware';
import { isGmailAuthenticated } from '@/lib/gmail';

async function handleGetGmailAuthStatus(request: AuthenticatedRequest) {
  try {
    const isAuthenticated = isGmailAuthenticated();
    
    return NextResponse.json({ 
      success: true, 
      isAuthenticated 
    });
  } catch (error) {
    console.error('Error checking Gmail auth status:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to check Gmail auth status' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return withAuth(request, handleGetGmailAuthStatus);
}
