import { NextRequest, NextResponse } from 'next/server';
import { withAuth, AuthenticatedRequest } from '@/lib/jwt-middleware';
import { getAuthUrl } from '@/lib/gmail';

async function handleGetGmailAuthUrl(request: AuthenticatedRequest) {
  try {
    const authUrl = getAuthUrl();
    
    return NextResponse.json({ 
      success: true, 
      authUrl 
    });
  } catch (error) {
    console.error('Error generating Gmail auth URL:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to generate Gmail auth URL' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  return withAuth(request, handleGetGmailAuthUrl);
}
